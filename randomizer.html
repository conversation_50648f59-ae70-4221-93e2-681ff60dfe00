<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reasons I Love You - Happy Birthday Mannu</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💖</text></svg>">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <div class="heart-badge">💖</div>
                <span>Happy Birthday Mannu</span>
            </a>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="quiz.html">Quiz</a></li>
                    <li><a href="randomizer.html" class="active">Why I Love You</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="randomizer-container">
            <!-- Page Header -->
            <div class="card mb-3 text-center">
                <h2>💝 All the Ways You Make My Heart Skip</h2>
                <p class="subtitle">
                    Honestly, I could write a whole book about why you're incredible, but let's start with some random reasons that pop into my head.
                    Hit that heart when you find ones that make you smile! 💕
                </p>
            </div>

            <!-- Reason Display Card -->
            <div class="reason-card">
                <button class="heart-btn" id="heart-btn" title="Save to favorites">
                    🤍
                </button>
                <div class="reason-text" id="reason-text">
                    Ready to see why you make my heart do those little happy flips? Hit that button and let's find out! ✨
                </div>
                <button class="btn btn-primary mt-3" id="new-reason-btn">
                    🎲 Give Me Another One
                </button>
            </div>

            <!-- Favorites Section -->
            <div class="favorites-section">
                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <h3>💖 The Ones That Made You Smile</h3>
                        <button class="btn btn-small btn-secondary" id="clear-favorites" style="display: none;">
                            🗑️ Clear All
                        </button>
                    </div>
                    
                    <div id="favorites-container">
                        <p style="color: var(--gray); font-style: italic; text-align: center;">
                            Nothing saved yet! Hit those hearts on the reasons that make you grin 💕
                        </p>
                    </div>
                    
                    <div class="favorites-grid" id="favorites-grid">
                        <!-- Favorites will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Back to Other Pages -->
            <div class="text-center mt-4">
                <a href="quiz.html" class="btn btn-secondary mr-2">
                    🎯 Try That Quiz
                </a>
                <a href="index.html" class="btn btn-secondary">
                    🏠 Take Me Home
                </a>
            </div>
        </div>
    </main>

    <script src="js/floating-animation.js"></script>
    <script src="js/randomizer.js"></script>
</body>
</html>
