<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy Birthday Mannu</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💖</text></svg>">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <div class="heart-badge">💖</div>
                <span>Happy Birthday Mannu</span>
            </a>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html" class="active">Home</a></li>
                    <li><a href="quiz.html">Quiz</a></li>
                    <li><a href="randomizer.html">Why I Love You</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card birthday-card-1" data-animation="roll">
                <h1>Happy Birthday Mannu! 🎉</h1>
                <p class="subtitle">
                    I know things between us are... complicated right now. But it's your birthday, and despite everything we've been through,
                    I still wanted to do something special for you. You've always meant so much to me, and that hasn't changed.
                </p>

                <div class="birthday-actions">
                    <a href="quiz.html" class="btn btn-primary mb-3">
                        🎯 A Little Quiz About Us
                    </a>

                    <p class="mb-3" style="color: var(--gray); font-size: 0.9rem;">
                        6 questions about where we've been and where we might be going (no pressure, just honest thoughts)
                    </p>
                    
                    <div class="birthday-card-2" data-animation="slide" style="margin: 2rem 0; padding: 1rem; background: var(--soft-pink); border-radius: 15px;">
                        <h3 style="margin-bottom: 1rem;">✨ What I've put together for your special day:</h3>
                        <div style="display: grid; gap: 1rem; text-align: left; max-width: 400px; margin: 0 auto;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: var(--accent-pink);">🎯</span>
                                <span>Some questions about our journey and what I've learned</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: var(--accent-pink);">💕</span>
                                <span>Reasons why I still care, despite everything</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: var(--accent-pink);">🎊</span>
                                <span>A birthday wish from someone who still believes in us</span>
                            </div>
                        </div>
                    </div>
                    
                    <a href="randomizer.html" class="btn btn-secondary">
                        💝 Why You Still Mean Everything to Me
                    </a>
                </div>
            </div>
            
            <!-- Birthday Message Card -->
            <div class="card birthday-card-3 mt-4" data-animation="flip" style="max-width: 500px; margin: 2rem auto 0;">
                <h3 style="color: var(--dark-pink); margin-bottom: 1rem;">Something I need you to know 💌</h3>
                <p style="font-style: italic; color: var(--gray); line-height: 1.8;">
                    "I know we've been through so much together - three years that meant everything, two years apart that taught us both a lot, and now... this uncertain but hopeful place we find ourselves in.
                    I don't know exactly where we're headed, and I'm not trying to pressure anything. I just know that having you back in my life, even in this complicated way, feels like a gift I didn't expect to get again.
                    Today's your birthday, and regardless of everything else, you deserve to feel celebrated and loved. Because you are, Mannu. You always have been."
                </p>
                <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid var(--soft-pink); color: var(--primary-pink); font-weight: 500;">
                    With hope and so much care ❤️
                </div>
            </div>
        </div>
    </main>

    <script src="js/floating-animation.js"></script>
    <script>
        // Birthday Card Rolling Animation System
        class BirthdayCardAnimations {
            constructor() {
                this.hasVisited = localStorage.getItem('mannu-birthday-visited');
                this.animationCards = document.querySelectorAll('[data-animation]');
                this.init();
            }

            init() {
                // Only run entrance animations on first visit
                if (!this.hasVisited) {
                    this.setupEntranceAnimations();
                    localStorage.setItem('mannu-birthday-visited', 'true');
                } else {
                    // For subsequent visits, just show cards normally
                    this.showCardsImmediately();
                }
            }

            setupEntranceAnimations() {
                // Add a small delay before starting animations to ensure page is fully loaded
                setTimeout(() => {
                    this.animationCards.forEach((card, index) => {
                        const animationType = card.dataset.animation;
                        const delay = index * 400; // 400ms stagger between cards

                        // Initially hide the card
                        card.style.opacity = '0';
                        card.style.transform = this.getInitialTransform(animationType);

                        // Trigger animation after delay
                        setTimeout(() => {
                            this.animateCard(card, animationType);
                        }, delay);
                    });
                }, 200); // Small initial delay
            }

            getInitialTransform(animationType) {
                switch(animationType) {
                    case 'roll':
                        return window.innerWidth <= 768
                            ? 'translateY(-100vh) rotateX(-90deg) scale(0.5)'
                            : 'translateX(-100vw) rotateY(-90deg) scale(0.5)';
                    case 'slide':
                        return 'translateY(100px) scale(0.8) rotateX(20deg)';
                    case 'flip':
                        return window.innerWidth <= 768
                            ? 'translateY(100vh) rotateX(90deg) scale(0.5)'
                            : 'translateX(100vw) rotateY(90deg) scale(0.5)';
                    default:
                        return 'translateY(50px) scale(0.8)';
                }
            }

            animateCard(card, animationType) {
                // Add the appropriate animation class
                const animationClass = `card-${animationType}-entrance`;
                card.classList.add(animationClass);

                // Add celebration pulse after animation completes
                setTimeout(() => {
                    if (animationType === 'roll') {
                        card.classList.add('celebration-pulse');
                        // Remove pulse after a few cycles
                        setTimeout(() => {
                            card.classList.remove('celebration-pulse');
                        }, 4000);
                    }
                }, 1200); // Duration of entrance animation
            }

            showCardsImmediately() {
                // For return visits, just show cards without animation
                this.animationCards.forEach(card => {
                    card.style.opacity = '1';
                    card.style.transform = 'none';
                });
            }
        }

        // Add some interactive sparkle effects
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize birthday card animations
            new BirthdayCardAnimations();

            // Debug function for testing animations (remove in production)
            window.resetBirthdayAnimation = function() {
                localStorage.removeItem('mannu-birthday-visited');
                location.reload();
            };

            // Add sparkle animation to the title
            const title = document.querySelector('h1');
            if (title) {
                title.addEventListener('mouseenter', function() {
                    this.style.animation = 'titleGlow 0.5s ease-in-out';
                });
                
                title.addEventListener('animationend', function() {
                    this.style.animation = 'titleGlow 2s ease-in-out infinite alternate';
                });
            }

            // Add button press effects
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Stagger card animations
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });

        // Add ripple effect styles dynamically
        const style = document.createElement('style');
        style.textContent = `
            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            }
            
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
