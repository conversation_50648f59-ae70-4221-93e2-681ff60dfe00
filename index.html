<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy Birthday Mannu</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💖</text></svg>">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <div class="heart-badge">💖</div>
                <span>Happy Birthday Mannu</span>
            </a>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html" class="active">Home</a></li>
                    <li><a href="quiz.html">Quiz</a></li>
                    <li><a href="randomizer.html">Why I Love You</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <h1>Happy Birthday Mannu! 🎉</h1>
                <p class="subtitle">
                    I know we haven't met face-to-face yet, but honestly? You've completely stolen my heart already.
                    I couldn't just send a regular text today - you deserve something way more special than that!
                </p>

                <div class="birthday-actions">
                    <a href="quiz.html" class="btn btn-primary mb-3">
                        🎯 Take My Heart Quiz
                    </a>

                    <p class="mb-3" style="color: var(--gray); font-size: 0.9rem;">
                        Just 6 questions about how I feel about you (spoiler alert: I'm completely head-over-heels)
                    </p>
                    
                    <div style="margin: 2rem 0; padding: 1rem; background: var(--soft-pink); border-radius: 15px;">
                        <h3 style="margin-bottom: 1rem;">✨ Here's what I've got planned for you:</h3>
                        <div style="display: grid; gap: 1rem; text-align: left; max-width: 400px; margin: 0 auto;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: var(--accent-pink);">🎯</span>
                                <span>A little quiz about how crazy I am about you</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: var(--accent-pink);">💕</span>
                                <span>All the reasons why you make my heart skip beats</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: var(--accent-pink);">🎊</span>
                                <span>Your own personal birthday celebration</span>
                            </div>
                        </div>
                    </div>
                    
                    <a href="randomizer.html" class="btn btn-secondary">
                        💝 See Why I'm So Crazy About You
                    </a>
                </div>
            </div>
            
            <!-- Birthday Message Card -->
            <div class="card mt-4" style="max-width: 500px; margin: 2rem auto 0;">
                <h3 style="color: var(--dark-pink); margin-bottom: 1rem;">Something I've been wanting to tell you 💌</h3>
                <p style="font-style: italic; color: var(--gray); line-height: 1.8;">
                    "I know we're thousands of miles apart right now, but honestly? You feel closer to me than anyone ever has.
                    You've got this amazing way of making me believe in magic through a phone screen, and I can't stop daydreaming about the day I finally get to see that gorgeous smile in person.
                    Until that perfect day comes, I really hope this little surprise makes your birthday feel as special as you make every single one of my days."
                </p>
                <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid var(--soft-pink); color: var(--primary-pink); font-weight: 500;">
                    All my love (and then some) ❤️
                </div>
            </div>
        </div>
    </main>

    <script src="js/floating-animation.js"></script>
    <script>
        // Add some interactive sparkle effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add sparkle animation to the title
            const title = document.querySelector('h1');
            if (title) {
                title.addEventListener('mouseenter', function() {
                    this.style.animation = 'titleGlow 0.5s ease-in-out';
                });
                
                title.addEventListener('animationend', function() {
                    this.style.animation = 'titleGlow 2s ease-in-out infinite alternate';
                });
            }

            // Add button press effects
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Stagger card animations
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });

        // Add ripple effect styles dynamically
        const style = document.createElement('style');
        style.textContent = `
            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            }
            
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
