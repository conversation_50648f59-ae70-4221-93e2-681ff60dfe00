// Reasons I Love You - Randomizer Functionality
const loveReasons = [
    "The way your voice makes everything better, even when it's just coming through my phone",
    "How you send me those good morning texts that literally make my entire day brighter",
    "Your laugh - honestly, I could listen to it on repeat forever and never get sick of it",
    "How you actually remember every tiny detail I tell you, even the completely random stuff",
    "The way you make me feel like I'm the most important person in your whole world",
    "How our late-night video calls have become the absolute best part of my day",
    "Your incredible talent for making me smile even when I'm having the absolute worst day",
    "How you actually listen when I ramble about the most random things and seem genuinely interested",
    "The way you get so excited about little things - it's honestly the most adorable thing ever",
    "How you make me feel understood in ways I didn't even know were possible",
    "Your texts that show up at exactly the right moment, like you've got some kind of mind-reading superpower",
    "How you make thousands of miles feel like absolutely nothing when we're talking",
    "The way you cheer on my dreams even though we're so far apart",
    "How you make me laugh until I'm literally crying happy tears",
    "Your kindness that just radiates through every single message you send",
    "How you turn the most ordinary conversations into the highlight of my entire day",
    "The way you remember stuff I mentioned like three weeks ago - it means absolutely everything to me",
    "How you make me feel way less alone, even when we're in completely different time zones",
    "Your amazing superpower of turning my terrible moods into good ones",
    "How you make me so excited about our future, especially that magical day when we finally meet",
    "The way you care about my family and friends like they're already your own",
    "How you make me feel brave enough to be my real, authentic, messy self with you",
    "Those sweet voice messages you send that I definitely save and replay when I'm missing you like crazy",
    "How you've made me believe in this kind of love in ways I honestly never did before",
    "The way you inspire me to be better - not because I have to, but because you make me want to",
    "How you take my completely random thoughts and somehow turn them into these deep, meaningful conversations",
    "Your endless patience with me when I'm being difficult or in one of my moods",
    "How you make me feel like home is wherever you are, even if that's thousands of miles away",
    "The way you make me count down the days until we can finally be in the same place",
    "How you love me for exactly who I am - all my flaws, quirks, and weird habits included"
];

class LoveRandomizer {
    constructor() {
        this.currentReason = null;
        this.favorites = this.loadFavorites();
        this.usedReasons = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.displayFavorites();
    }

    initializeElements() {
        this.reasonText = document.getElementById('reason-text');
        this.heartBtn = document.getElementById('heart-btn');
        this.newReasonBtn = document.getElementById('new-reason-btn');
        this.favoritesContainer = document.getElementById('favorites-container');
        this.favoritesGrid = document.getElementById('favorites-grid');
        this.clearFavoritesBtn = document.getElementById('clear-favorites');
    }

    setupEventListeners() {
        this.newReasonBtn.addEventListener('click', () => this.showNewReason());
        this.heartBtn.addEventListener('click', () => this.toggleFavorite());
        this.clearFavoritesBtn.addEventListener('click', () => this.clearAllFavorites());
    }

    showNewReason() {
        // Add loading state
        this.newReasonBtn.classList.add('loading');
        this.newReasonBtn.innerHTML = '🎲 Hold on<span class="spinner"></span>';

        setTimeout(() => {
            const reason = this.getRandomReason();
            this.currentReason = reason;

            // Animate reason change
            this.reasonText.style.opacity = '0';
            this.reasonText.style.transform = 'translateY(20px)';

            setTimeout(() => {
                this.reasonText.textContent = reason;
                this.reasonText.style.opacity = '1';
                this.reasonText.style.transform = 'translateY(0)';
                this.updateHeartButton();

                // Remove loading state
                this.newReasonBtn.classList.remove('loading');
                this.newReasonBtn.innerHTML = '🎲 Give Me Another One';
            }, 200);
        }, 500);
    }

    getRandomReason() {
        // If we've used all reasons, reset the used array
        if (this.usedReasons.length >= loveReasons.length) {
            this.usedReasons = [];
        }
        
        // Get available reasons
        const availableReasons = loveReasons.filter((reason, index) => 
            !this.usedReasons.includes(index)
        );
        
        // Pick a random available reason
        const randomIndex = Math.floor(Math.random() * availableReasons.length);
        const selectedReason = availableReasons[randomIndex];
        
        // Mark this reason as used
        const originalIndex = loveReasons.indexOf(selectedReason);
        this.usedReasons.push(originalIndex);
        
        return selectedReason;
    }

    updateHeartButton() {
        if (this.currentReason && this.favorites.includes(this.currentReason)) {
            this.heartBtn.textContent = '❤️';
            this.heartBtn.classList.add('favorited');
            this.heartBtn.title = 'Remove from favorites';
        } else {
            this.heartBtn.textContent = '🤍';
            this.heartBtn.classList.remove('favorited');
            this.heartBtn.title = 'Save to favorites';
        }
    }

    toggleFavorite() {
        if (!this.currentReason) return;
        
        const index = this.favorites.indexOf(this.currentReason);
        
        if (index > -1) {
            // Remove from favorites
            this.favorites.splice(index, 1);
            this.heartBtn.style.animation = 'heartBreak 0.3s ease';
        } else {
            // Add to favorites
            this.favorites.push(this.currentReason);
            this.heartBtn.style.animation = 'heartBeat 0.3s ease';

            // Trigger a mini celebration
            if (window.floatingAnimation) {
                window.floatingAnimation.celebrate(2000); // 2 seconds of increased activity
            }
        }
        
        this.saveFavorites();
        this.updateHeartButton();
        this.displayFavorites();
        
        // Reset animation
        setTimeout(() => {
            this.heartBtn.style.animation = '';
        }, 300);
    }

    loadFavorites() {
        const saved = localStorage.getItem('love-favorites');
        return saved ? JSON.parse(saved) : [];
    }

    saveFavorites() {
        localStorage.setItem('love-favorites', JSON.stringify(this.favorites));
    }

    displayFavorites() {
        if (this.favorites.length === 0) {
            this.favoritesContainer.innerHTML = `
                <p style="color: var(--gray); font-style: italic; text-align: center;">
                    Nothing saved yet! Hit those hearts on the reasons that make you grin 💕
                </p>
            `;
            this.favoritesGrid.innerHTML = '';
            this.clearFavoritesBtn.style.display = 'none';
        } else {
            this.favoritesContainer.innerHTML = `
                <p style="color: var(--dark-pink); text-align: center; margin-bottom: 1rem;">
                    You've saved ${this.favorites.length} reason${this.favorites.length !== 1 ? 's' : ''} that made you smile! 💖
                </p>
            `;
            
            this.favoritesGrid.innerHTML = '';
            this.favorites.forEach((reason, index) => {
                const favoriteItem = document.createElement('div');
                favoriteItem.className = 'favorite-item';
                favoriteItem.innerHTML = `
                    <button class="remove-favorite" data-index="${index}" title="Remove from favorites">×</button>
                    <div>${reason}</div>
                `;
                
                // Add remove functionality
                const removeBtn = favoriteItem.querySelector('.remove-favorite');
                removeBtn.addEventListener('click', () => this.removeFavorite(index));
                
                this.favoritesGrid.appendChild(favoriteItem);
            });
            
            this.clearFavoritesBtn.style.display = 'inline-block';
        }
    }

    removeFavorite(index) {
        this.favorites.splice(index, 1);
        this.saveFavorites();
        this.displayFavorites();
        this.updateHeartButton();
    }

    clearAllFavorites() {
        if (confirm('Are you sure you want to clear all your favorite reasons? This cannot be undone.')) {
            this.favorites = [];
            this.saveFavorites();
            this.displayFavorites();
            this.updateHeartButton();
        }
    }
}

// Initialize randomizer when page loads
document.addEventListener('DOMContentLoaded', function() {
    const randomizer = new LoveRandomizer();
    
    // Add heart animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes heartBeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }
        
        @keyframes heartBreak {
            0% { transform: scale(1); }
            25% { transform: scale(0.8) rotate(-10deg); }
            50% { transform: scale(0.9) rotate(10deg); }
            75% { transform: scale(0.85) rotate(-5deg); }
            100% { transform: scale(1) rotate(0deg); }
        }
        
        .mr-2 {
            margin-right: 1rem;
        }
    `;
    document.head.appendChild(style);
});
