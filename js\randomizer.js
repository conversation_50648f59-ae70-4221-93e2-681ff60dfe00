// Reasons I Love You - Randomizer Functionality
const loveReasons = [
    "Because you came back into my life when you didn't have to, and that means more than you know",
    "The way you're giving us another chance, even after everything that went wrong before",
    "How those three years we had together still feel like the most real thing I've ever experienced",
    "Because losing you taught me what I had, and having you back reminds me what's possible",
    "The courage it must have taken for you to reach out again after two years of silence",
    "How you're letting me prove that I've learned from the mistakes I made before",
    "Because even in this uncertain space we're in, you still feel like home to me",
    "The way you're being patient with this complicated situation we find ourselves in",
    "How you still laugh at my jokes, even when things are awkward between us",
    "Because you deserve someone who appreciates what we had instead of taking it for granted",
    "The way you're protecting your heart while still being open to possibilities",
    "How you've grown during our time apart - I can see the strength you've gained",
    "Because you taught me that some connections are worth fighting for, even after they break",
    "The way you still remember little things about me, despite everything we've been through",
    "How you're giving me the chance to show you I'm not the same person who let you down",
    "Because you're here on your birthday, letting me celebrate you despite our complicated history",
    "The way you handle our ups and downs with such grace and maturity",
    "How you've shown me what forgiveness looks like, even when it's hard",
    "Because you still see something worth saving in what we had together",
    "The way you're taking this slow and being careful with both our hearts",
    "How you've taught me that love isn't just about the good times - it's about coming back",
    "Because you're braver than you know for giving us another chance to get it right",
    "The way you still care enough to work through the difficult conversations with me",
    "How you've shown me that some people are worth waiting for, worth changing for",
    "Because you're here, in this messy, uncertain, hopeful place with me",
    "The way you've made me believe that second chances can be even more beautiful than first ones",
    "How you've taught me that real love means growing through the hard times together",
    "Because you still see the good in us, even when it's buried under hurt and confusion",
    "The way you make me want to be worthy of the trust you're slowly rebuilding in me",
    "How you've shown me that coming back is sometimes harder and braver than never leaving at all"
];

class LoveRandomizer {
    constructor() {
        this.currentReason = null;
        this.favorites = this.loadFavorites();
        this.usedReasons = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.displayFavorites();
    }

    initializeElements() {
        this.reasonText = document.getElementById('reason-text');
        this.heartBtn = document.getElementById('heart-btn');
        this.newReasonBtn = document.getElementById('new-reason-btn');
        this.favoritesContainer = document.getElementById('favorites-container');
        this.favoritesGrid = document.getElementById('favorites-grid');
        this.clearFavoritesBtn = document.getElementById('clear-favorites');
    }

    setupEventListeners() {
        this.newReasonBtn.addEventListener('click', () => this.showNewReason());
        this.heartBtn.addEventListener('click', () => this.toggleFavorite());
        this.clearFavoritesBtn.addEventListener('click', () => this.clearAllFavorites());
    }

    showNewReason() {
        // Add loading state
        this.newReasonBtn.classList.add('loading');
        this.newReasonBtn.innerHTML = '🎲 Hold on<span class="spinner"></span>';

        setTimeout(() => {
            const reason = this.getRandomReason();
            this.currentReason = reason;

            // Animate reason change
            this.reasonText.style.opacity = '0';
            this.reasonText.style.transform = 'translateY(20px)';

            setTimeout(() => {
                this.reasonText.textContent = reason;
                this.reasonText.style.opacity = '1';
                this.reasonText.style.transform = 'translateY(0)';
                this.updateHeartButton();

                // Remove loading state
                this.newReasonBtn.classList.remove('loading');
                this.newReasonBtn.innerHTML = '🎲 Show Me Another Reason';
            }, 200);
        }, 500);
    }

    getRandomReason() {
        // If we've used all reasons, reset the used array
        if (this.usedReasons.length >= loveReasons.length) {
            this.usedReasons = [];
        }
        
        // Get available reasons
        const availableReasons = loveReasons.filter((reason, index) => 
            !this.usedReasons.includes(index)
        );
        
        // Pick a random available reason
        const randomIndex = Math.floor(Math.random() * availableReasons.length);
        const selectedReason = availableReasons[randomIndex];
        
        // Mark this reason as used
        const originalIndex = loveReasons.indexOf(selectedReason);
        this.usedReasons.push(originalIndex);
        
        return selectedReason;
    }

    updateHeartButton() {
        if (this.currentReason && this.favorites.includes(this.currentReason)) {
            this.heartBtn.textContent = '❤️';
            this.heartBtn.classList.add('favorited');
            this.heartBtn.title = 'Remove from favorites';
        } else {
            this.heartBtn.textContent = '🤍';
            this.heartBtn.classList.remove('favorited');
            this.heartBtn.title = 'Save to favorites';
        }
    }

    toggleFavorite() {
        if (!this.currentReason) return;
        
        const index = this.favorites.indexOf(this.currentReason);
        
        if (index > -1) {
            // Remove from favorites
            this.favorites.splice(index, 1);
            this.heartBtn.style.animation = 'heartBreak 0.3s ease';
        } else {
            // Add to favorites
            this.favorites.push(this.currentReason);
            this.heartBtn.style.animation = 'heartBeat 0.3s ease';

            // Trigger a mini celebration
            if (window.floatingAnimation) {
                window.floatingAnimation.celebrate(2000); // 2 seconds of increased activity
            }
        }
        
        this.saveFavorites();
        this.updateHeartButton();
        this.displayFavorites();
        
        // Reset animation
        setTimeout(() => {
            this.heartBtn.style.animation = '';
        }, 300);
    }

    loadFavorites() {
        const saved = localStorage.getItem('love-favorites');
        return saved ? JSON.parse(saved) : [];
    }

    saveFavorites() {
        localStorage.setItem('love-favorites', JSON.stringify(this.favorites));
    }

    displayFavorites() {
        if (this.favorites.length === 0) {
            this.favoritesContainer.innerHTML = `
                <p style="color: var(--gray); font-style: italic; text-align: center;">
                    Nothing saved yet! Hit those hearts on the reasons that make you grin 💕
                </p>
            `;
            this.favoritesGrid.innerHTML = '';
            this.clearFavoritesBtn.style.display = 'none';
        } else {
            this.favoritesContainer.innerHTML = `
                <p style="color: var(--dark-pink); text-align: center; margin-bottom: 1rem;">
                    You've saved ${this.favorites.length} thought${this.favorites.length !== 1 ? 's' : ''} that resonated with you 💖
                </p>
            `;
            
            this.favoritesGrid.innerHTML = '';
            this.favorites.forEach((reason, index) => {
                const favoriteItem = document.createElement('div');
                favoriteItem.className = 'favorite-item';
                favoriteItem.innerHTML = `
                    <button class="remove-favorite" data-index="${index}" title="Remove from favorites">×</button>
                    <div>${reason}</div>
                `;
                
                // Add remove functionality
                const removeBtn = favoriteItem.querySelector('.remove-favorite');
                removeBtn.addEventListener('click', () => this.removeFavorite(index));
                
                this.favoritesGrid.appendChild(favoriteItem);
            });
            
            this.clearFavoritesBtn.style.display = 'inline-block';
        }
    }

    removeFavorite(index) {
        this.favorites.splice(index, 1);
        this.saveFavorites();
        this.displayFavorites();
        this.updateHeartButton();
    }

    clearAllFavorites() {
        if (confirm('Are you sure you want to clear all your favorite reasons? This cannot be undone.')) {
            this.favorites = [];
            this.saveFavorites();
            this.displayFavorites();
            this.updateHeartButton();
        }
    }
}

// Initialize randomizer when page loads
document.addEventListener('DOMContentLoaded', function() {
    const randomizer = new LoveRandomizer();
    
    // Add heart animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes heartBeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }
        
        @keyframes heartBreak {
            0% { transform: scale(1); }
            25% { transform: scale(0.8) rotate(-10deg); }
            50% { transform: scale(0.9) rotate(10deg); }
            75% { transform: scale(0.85) rotate(-5deg); }
            100% { transform: scale(1) rotate(0deg); }
        }
        
        .mr-2 {
            margin-right: 1rem;
        }
    `;
    document.head.appendChild(style);
});
