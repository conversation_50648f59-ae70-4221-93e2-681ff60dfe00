// Floating Animation System - Instagram Live Style
class FloatingAnimation {
    constructor() {
        this.container = null;
        this.isActive = false;
        this.animationInterval = null;
        
        // Animation elements
        this.hearts = ['💖', '❤️', '💕', '💗', '🤍', '💝', '💘'];
        this.kisses = ['💋', '😘', '😗', '😙'];
        this.texts = [
            'Happy Birthday Mannu!',
            'You\'re Amazing!',
            'Love You!',
            'So Special!',
            'My Heart!',
            'Beautiful Soul!',
            'Sweet Dreams!',
            'Missing You!'
        ];
        
        // Pastel pink color palette for text
        this.textColors = [
            '#FFB6C1', // Light pink
            '#FFC0CB', // Pink
            '#FF69B4', // Hot pink
            '#DB7093', // Pale violet red
            '#FFE4E1', // Misty rose
            '#F8BBD9', // Light pink
            '#E6A8D0', // Medium pink
            '#D291BC'  // Soft pink
        ];
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.startAnimation();
    }
    
    createContainer() {
        // Remove existing container if it exists
        const existing = document.querySelector('.floating-container');
        if (existing) {
            existing.remove();
        }
        
        // Create new container
        this.container = document.createElement('div');
        this.container.className = 'floating-container';
        document.body.appendChild(this.container);
    }
    
    startAnimation() {
        if (this.isActive) return;
        
        this.isActive = true;
        this.scheduleNextElement();
    }
    
    stopAnimation() {
        this.isActive = false;
        if (this.animationInterval) {
            clearTimeout(this.animationInterval);
            this.animationInterval = null;
        }
    }
    
    scheduleNextElement() {
        if (!this.isActive) return;

        // Much more frequent: Random delay between 0.3-1 seconds
        const delay = Math.random() * 700 + 300;

        this.animationInterval = setTimeout(() => {
            this.createFloatingElement();
            this.scheduleNextElement();
        }, delay);
    }
    
    createFloatingElement() {
        const element = document.createElement('div');
        element.className = 'floating-element';
        
        // Randomly choose element type
        const types = ['heart', 'heart', 'heart', 'kiss', 'text']; // Hearts more frequent
        const type = types[Math.floor(Math.random() * types.length)];
        
        // Set content based on type
        if (type === 'heart') {
            element.textContent = this.hearts[Math.floor(Math.random() * this.hearts.length)];
            element.classList.add('heart');
        } else if (type === 'kiss') {
            element.textContent = this.kisses[Math.floor(Math.random() * this.kisses.length)];
            element.classList.add('kiss');
        } else {
            element.textContent = this.texts[Math.floor(Math.random() * this.texts.length)];
            element.classList.add('text');
            element.style.color = this.textColors[Math.floor(Math.random() * this.textColors.length)];
        }
        
        // Random size
        const sizes = ['small', 'medium', 'large'];
        const size = sizes[Math.floor(Math.random() * sizes.length)];
        element.classList.add(size);
        
        // Random starting position (bottom area, more spread to the left)
        const startX = Math.random() * 200 - 50; // -50px to 150px from left edge of container
        const startY = window.innerHeight + 20; // Start below viewport
        
        element.style.left = startX + 'px';
        element.style.top = startY + 'px';
        
        // Random animation type
        const animations = ['floatUp', 'floatUpLeft', 'floatUpRight'];
        const animation = animations[Math.floor(Math.random() * animations.length)];
        
        // Random duration between 4-8 seconds (longer to have more on screen)
        const duration = Math.random() * 4000 + 4000;
        
        element.style.animation = `${animation} ${duration}ms ease-out forwards`;
        
        // Add to container
        this.container.appendChild(element);
        
        // Remove element after animation completes
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, duration + 100);
    }
    
    // Method to temporarily increase animation frequency (for special moments)
    celebrate(duration = 5000) {
        const originalSchedule = this.scheduleNextElement.bind(this);
        
        // Increase frequency during celebration
        this.scheduleNextElement = () => {
            if (!this.isActive) return;

            const delay = Math.random() * 200 + 100; // Super fast: 100-300ms

            this.animationInterval = setTimeout(() => {
                this.createFloatingElement();
                this.scheduleNextElement();
            }, delay);
        };
        
        // Reset to normal after duration
        setTimeout(() => {
            this.scheduleNextElement = originalSchedule;
        }, duration);
    }
    
    // Clean up method
    destroy() {
        this.stopAnimation();
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}

// Global instance
let floatingAnimation = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure page is fully loaded
    setTimeout(() => {
        floatingAnimation = new FloatingAnimation();
    }, 1000);
});

// Handle page visibility changes to pause/resume animation
document.addEventListener('visibilitychange', function() {
    if (floatingAnimation) {
        if (document.hidden) {
            floatingAnimation.stopAnimation();
        } else {
            floatingAnimation.startAnimation();
        }
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (floatingAnimation) {
        floatingAnimation.destroy();
    }
});

// Export for use in other scripts
window.FloatingAnimation = FloatingAnimation;
window.floatingAnimation = floatingAnimation;
