/* Happy Birthday Mannu - Main Styles */
/* @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap'); */

:root {
  /* Pastel Pink Color Palette */
  --primary-pink: #FFB6C1;
  --light-pink: #FFC0CB;
  --soft-pink: #FFE4E1;
  --pale-pink: #FFF0F5;
  --accent-pink: #FF69B4;
  --dark-pink: #DB7093;
  
  /* Supporting Colors */
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --gray: #6C757D;
  --dark-gray: #495057;
  --success: #D4EDDA;
  --error: #F8D7DA;
  
  /* Shadows and Effects */
  --shadow-soft: 0 4px 20px rgba(255, 182, 193, 0.15);
  --shadow-medium: 0 8px 30px rgba(255, 182, 193, 0.2);
  --shadow-strong: 0 12px 40px rgba(255, 182, 193, 0.25);
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--light-pink) 0%, var(--primary-pink) 100%);
  --gradient-soft: linear-gradient(135deg, var(--pale-pink) 0%, var(--soft-pink) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-pink) 0%, var(--dark-pink) 100%);
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Animation Durations */
  --animation-duration: 1.2s;
  --animation-stagger: 0.3s;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: var(--gradient-soft);
  min-height: 100vh;
  color: var(--dark-gray);
  line-height: 1.6;
}

/* Header Styles */
.header {
  background: var(--white);
  box-shadow: var(--shadow-soft);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--dark-pink);
  text-decoration: none;
  font-size: 1.2rem;
}

.heart-badge {
  width: 30px;
  height: 30px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1rem;
}

.nav-links {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-links a {
  color: var(--gray);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.nav-links a:hover,
.nav-links a.active {
  color: var(--dark-pink);
  background: var(--soft-pink);
}

/* Container and Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.main-content {
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem 0;
}

/* Card Styles */
.card {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
  transition: var(--transition-medium);
  animation: cardEntrance 0.6s ease-out;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-strong);
}

@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Typography */
h1 {
  font-size: 3rem;
  font-weight: 700;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  from {
    filter: drop-shadow(0 0 10px rgba(255, 105, 180, 0.3));
  }
  to {
    filter: drop-shadow(0 0 20px rgba(255, 105, 180, 0.5));
  }
}

h2 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--dark-pink);
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--dark-pink);
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.2rem;
  color: var(--gray);
  margin-bottom: 2rem;
  font-weight: 400;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-strong);
}

.btn-secondary {
  background: var(--white);
  color: var(--dark-pink);
  border: 2px solid var(--primary-pink);
}

.btn-secondary:hover {
  background: var(--soft-pink);
  transform: translateY(-2px);
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* Button Press Animation */
.btn:active {
  transform: scale(0.95);
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

/* Progress Bar */
.progress-container {
  width: 100%;
  height: 8px;
  background: var(--soft-pink);
  border-radius: 4px;
  overflow: hidden;
  margin: 1rem 0;
}

.progress-bar {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 4px;
  transition: width var(--transition-medium);
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Quiz Styles */
.quiz-container {
  max-width: 800px;
  margin: 0 auto;
}

.question-card {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
  margin-bottom: 2rem;
}

.question-number {
  color: var(--primary-pink);
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.question-text {
  font-size: 1.3rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 1.5rem;
}

.answers-grid {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.answer-option {
  background: var(--pale-pink);
  border: 2px solid transparent;
  border-radius: 15px;
  padding: 1rem;
  cursor: pointer;
  transition: var(--transition-fast);
  font-weight: 500;
}

.answer-option:hover {
  background: var(--soft-pink);
  border-color: var(--primary-pink);
  transform: translateY(-2px);
}

.answer-option.selected {
  background: var(--primary-pink);
  color: var(--white);
  border-color: var(--dark-pink);
  animation: answerSelect 0.3s ease;
}

@keyframes answerSelect {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.quiz-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

/* Randomizer Styles */
.randomizer-container {
  max-width: 600px;
  margin: 0 auto;
}

.reason-card {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
  margin-bottom: 2rem;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
}

.reason-text {
  font-size: 1.3rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 1rem;
  animation: reasonFadeIn 0.5s ease;
}

@keyframes reasonFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.heart-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--gray);
}

.heart-btn:hover,
.heart-btn.favorited {
  color: var(--accent-pink);
  transform: scale(1.2);
}

.favorites-section {
  margin-top: 2rem;
}

.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.favorite-item {
  background: var(--soft-pink);
  border-radius: 15px;
  padding: 1rem;
  font-size: 0.9rem;
  position: relative;
  animation: favoriteAdd 0.3s ease;
}

@keyframes favoriteAdd {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.remove-favorite {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  color: var(--gray);
  cursor: pointer;
  font-size: 0.8rem;
  transition: var(--transition-fast);
}

.remove-favorite:hover {
  color: var(--accent-pink);
}

/* Results Styles */
.results-card {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.score-display {
  font-size: 3rem;
  font-weight: 700;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.reveal-message {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--dark-pink);
  margin: 1.5rem 0;
  padding: 1rem;
  background: var(--soft-pink);
  border-radius: 15px;
  border-left: 4px solid var(--primary-pink);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  .header-content {
    padding: 0 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-links {
    gap: 1rem;
  }
  
  .quiz-navigation {
    flex-direction: column;
    gap: 1rem;
  }
  
  .answers-grid {
    grid-template-columns: 1fr;
  }
  
  .favorites-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) {
  .answers-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .two-column {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    align-items: start;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden {
  display: none;
}

.fade-in {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Confetti Canvas */
#confetti-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

/* Floating Animation Container */
.floating-container {
  position: fixed;
  top: 0;
  right: 0;
  width: 250px;
  height: 100vh;
  pointer-events: none;
  z-index: 50;
  overflow: hidden;
}

/* Floating Elements */
.floating-element {
  position: absolute;
  pointer-events: none;
  user-select: none;
  opacity: 0.8;
  animation-timing-function: ease-out;
  animation-fill-mode: forwards;
}

.floating-element.heart {
  font-size: 2.2rem;
}

.floating-element.kiss {
  font-size: 1.8rem;
}

.floating-element.text {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 1.1rem;
  white-space: nowrap;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.floating-element.small {
  transform: scale(0.8);
}

.floating-element.medium {
  transform: scale(1.2);
}

.floating-element.large {
  transform: scale(1.6);
}

/* Floating Animation Keyframes */
@keyframes floatUp {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0.8;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100vh) translateX(var(--drift-x, 0)) rotate(var(--rotation, 0deg));
    opacity: 0;
  }
}

@keyframes floatUpLeft {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0.8;
  }
  10% {
    opacity: 1;
  }
  50% {
    transform: translateY(-50vh) translateX(-20px) rotate(5deg);
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100vh) translateX(-40px) rotate(10deg);
    opacity: 0;
  }
}

@keyframes floatUpRight {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0.8;
  }
  10% {
    opacity: 1;
  }
  50% {
    transform: translateY(-50vh) translateX(20px) rotate(-5deg);
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100vh) translateX(40px) rotate(-10deg);
    opacity: 0;
  }
}

/* Mobile adjustments for floating elements */
@media (max-width: 768px) {
  .floating-container {
    width: 180px;
  }

  .floating-element.heart {
    font-size: 1.8rem;
  }

  .floating-element.kiss {
    font-size: 1.4rem;
  }

  .floating-element.text {
    font-size: 0.9rem;
  }
}

/* Loading States */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--soft-pink);
  border-top: 2px solid var(--primary-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-left: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus States for Accessibility */
.btn:focus,
.answer-option:focus,
.heart-btn:focus {
  outline: 2px solid var(--accent-pink);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-pink: #E91E63;
    --dark-pink: #AD1457;
    --gray: #424242;
    --dark-gray: #212121;
  }
}

/* Additional Polish Styles */
.birthday-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.quiz-breakdown {
  margin-top: 1.5rem;
}

.results-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

/* Mobile responsiveness for results */
@media (max-width: 768px) {
  .results-actions {
    flex-direction: column;
    align-items: center;
  }

  .results-actions .btn {
    width: 100%;
    max-width: 250px;
  }
}

/* Enhanced button hover effects */
.btn:hover {
  filter: brightness(1.05);
}

/* Smooth page transitions */
.page-transition {
  animation: pageEnter 0.5s ease-out;
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card Rolling Animation Effects */
.card-roll-entrance {
  opacity: 0;
  transform: translateX(-100vw) rotateY(-90deg);
  animation: cardRollIn var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform, opacity;
}

.card-slide-entrance {
  opacity: 0;
  transform: translateY(100px) scale(0.8);
  animation: cardSlideIn var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  will-change: transform, opacity;
}

.card-flip-entrance {
  opacity: 0;
  transform: translateX(100vw) rotateY(90deg);
  animation: cardFlipIn var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform, opacity;
}

/* Staggered animation delays */
.card-roll-entrance:nth-child(1) { animation-delay: 0s; }
.card-roll-entrance:nth-child(2) { animation-delay: var(--animation-stagger); }
.card-roll-entrance:nth-child(3) { animation-delay: calc(var(--animation-stagger) * 2); }

.card-slide-entrance:nth-child(1) { animation-delay: 0s; }
.card-slide-entrance:nth-child(2) { animation-delay: var(--animation-stagger); }
.card-slide-entrance:nth-child(3) { animation-delay: calc(var(--animation-stagger) * 2); }

.card-flip-entrance:nth-child(1) { animation-delay: 0s; }
.card-flip-entrance:nth-child(2) { animation-delay: var(--animation-stagger); }
.card-flip-entrance:nth-child(3) { animation-delay: calc(var(--animation-stagger) * 2); }

/* Keyframe Animations */
@keyframes cardRollIn {
  0% {
    opacity: 0;
    transform: translateX(-100vw) rotateY(-90deg) scale(0.5);
  }
  50% {
    opacity: 0.8;
    transform: translateX(0) rotateY(-45deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateX(0) rotateY(0deg) scale(1);
  }
}

@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(100px) scale(0.8) rotateX(20deg);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-10px) scale(0.95) rotateX(10deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
  }
}

@keyframes cardFlipIn {
  0% {
    opacity: 0;
    transform: translateX(100vw) rotateY(90deg) scale(0.5);
  }
  50% {
    opacity: 0.8;
    transform: translateX(0) rotateY(45deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateX(0) rotateY(0deg) scale(1);
  }
}

/* Birthday celebration enhancement */
@keyframes celebrationPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(255, 182, 193, 0.15);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(255, 182, 193, 0.25);
  }
}

.celebration-pulse {
  animation: celebrationPulse 2s ease-in-out infinite;
}

/* Ensure smooth animations on all devices */
.birthday-card-1,
.birthday-card-2,
.birthday-card-3 {
  transform-origin: center center;
  transition: transform 0.3s ease;
}

/* Add subtle hover effects that work with animations */
.birthday-card-1:hover,
.birthday-card-2:hover,
.birthday-card-3:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .card-roll-entrance {
    transform: translateY(-100vh) rotateX(-90deg);
    animation: cardRollInMobile var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .card-flip-entrance {
    transform: translateY(100vh) rotateX(90deg);
    animation: cardFlipInMobile var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  @keyframes cardRollInMobile {
    0% {
      opacity: 0;
      transform: translateY(-100vh) rotateX(-90deg) scale(0.5);
    }
    50% {
      opacity: 0.8;
      transform: translateY(0) rotateX(-45deg) scale(0.9);
    }
    100% {
      opacity: 1;
      transform: translateY(0) rotateX(0deg) scale(1);
    }
  }

  @keyframes cardFlipInMobile {
    0% {
      opacity: 0;
      transform: translateY(100vh) rotateX(90deg) scale(0.5);
    }
    50% {
      opacity: 0.8;
      transform: translateY(0) rotateX(45deg) scale(0.9);
    }
    100% {
      opacity: 1;
      transform: translateY(0) rotateX(0deg) scale(1);
    }
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .card-roll-entrance,
  .card-slide-entrance,
  .card-flip-entrance {
    animation: fadeInSimple 0.5s ease forwards;
  }

  @keyframes fadeInSimple {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
